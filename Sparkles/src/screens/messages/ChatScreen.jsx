import {
  View,
  Text,
  TextInput,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  RefreshControl,
  Keyboard,
} from 'react-native';
import {useRoute, useFocusEffect} from '@react-navigation/native';
import {height} from '../../utils/helpers';
import UserInfo from '../../components/Chat/UserInfo';
import Header from '../../components/Chat/Header';
import SvgAddImage from '../../assets/addImage';
import SvgSendBtn from '../../assets/sendBtn';
import React, {useEffect, useRef, useState, useMemo, useCallback} from 'react';
import Loader from '../../components/Loader';
import { useDispatch, useSelector } from 'react-redux';
import { fetchConversationMessages, sendMessage } from '../../redux/actions/messagesActions';
import { addNewMessage } from '../../redux/slices/messagesSlice';
import { markConversationAsRead } from '../../redux/slices/conversationsSlice';
import { markMessageAsRead } from '../../utils/readMessageStorage';
import FastImage from 'react-native-fast-image';

// Memoized Message Component for better performance
const MessageItem = React.memo(({
  item,
  index,
  currentUserId,
  currentUser,
  userInfo,
  messages,
  formatDate
}) => {
  // Determine if the message is from the current user
  const isCurrentUser = item.sender && (
    item.sender._id === 'currentUser' ||
    item.sender._id === currentUserId ||
    item.sender.id === currentUserId
  );
  const isMe = item.isMe || isCurrentUser;

  // Pre-calculate last message from other user (memoized)
  const lastOtherUserMessageIndex = useMemo(() => {
    return messages.findLastIndex(msg => {
      const msgSenderId = msg.sender && (msg.sender._id || msg.sender.id);
      return msgSenderId !== currentUserId && msgSenderId !== 'currentUser';
    });
  }, [messages, currentUserId]);

  const isLastMessageFromOtherUser = !isMe && index === lastOtherUserMessageIndex;

  // Get message text (memoized)
  const messageText = useMemo(() => {
    if (item && typeof item === 'object') {
      if (item.text !== undefined) return item.text;
      if (item.message !== undefined) return item.message;
      if (item.content !== undefined) return item.content;
      return 'Message content unavailable';
    }
    return 'Invalid message';
  }, [item]);

  // Format message date (memoized)
  const { messageDate, formattedMessageDate, showDateHeader } = useMemo(() => {
    const msgDate = item.createdAt ? new Date(item.createdAt) :
                   item.timestamp ? new Date(item.timestamp) : null;
    const formatted = msgDate ? formatDate(msgDate) : '';

    // Check if this is the first message of a new date group or time group
    const prevItem = index > 0 ? messages[index - 1] : null;
    const prevDate = prevItem?.createdAt ? new Date(prevItem.createdAt) :
                    prevItem?.timestamp ? new Date(prevItem.timestamp) : null;

    let showHeader = index === 0; // Always show for first message

    if (!showHeader && msgDate && prevDate) {
      // Show if date changed (different day, month, or year)
      const dateChanged = msgDate.getDate() !== prevDate.getDate() ||
                         msgDate.getMonth() !== prevDate.getMonth() ||
                         msgDate.getFullYear() !== prevDate.getFullYear();

      // Show if time gap is 4+ hours (4 * 60 * 60 * 1000 = 14400000 milliseconds)
      const timeDiffInMs = msgDate.getTime() - prevDate.getTime();
      const fourHoursInMs = 4 * 60 * 60 * 1000;
      const timeGapExceeded = timeDiffInMs >= fourHoursInMs;

      showHeader = dateChanged || timeGapExceeded;
    }

    return {
      messageDate: msgDate,
      formattedMessageDate: formatted,
      showDateHeader: showHeader
    };
  }, [item, index, messages, formatDate]);

  return (
    <>
      {/* Display date header for message groups */}
      {showDateHeader && formattedMessageDate && (
        <Text style={styles.messageDateText}>{formattedMessageDate}</Text>
      )}

      <View
        style={[
          styles.messageContainer,
          isMe ? styles.myMessageContainer : styles.otherMessageContainer,
        ]}>
        <View style={{flexDirection: 'row', alignItems: 'center'}}>
          {/* Show profile picture for the other user's messages */}
          {!isMe && (
            <FastImage
              source={
                isLastMessageFromOtherUser
                  ? (item.sender?.profilePicture
                      ? {
                          uri: item.sender.profilePicture,
                          priority: FastImage.priority.normal,
                          cache: FastImage.cacheControl.web
                        }
                      : userInfo?.userImage)
                  : null
              }
              style={
                isLastMessageFromOtherUser
                  ? styles.userAvatar
                  : styles.emptyAvatar
              }
              resizeMode={FastImage.resizeMode.cover}
            />
          )}
          <View
            style={[
              styles.messageBubble,
              isMe ? styles.myMessage : styles.otherMessage,
            ]}>
            <Text
              style={[
                styles.messageText,
                isMe && { color: '#FFFFFF' }
              ]}
              numberOfLines={null}
              ellipsizeMode="tail"
            >
              {messageText}
            </Text>
          </View>

          {/* Show profile picture for the current user's messages */}
          {isMe && isLastMessageFromOtherUser && (
            <FastImage
              source={
                currentUser?.profilePicture
                  ? {
                      uri: currentUser.profilePicture,
                      priority: FastImage.priority.normal,
                      cache: FastImage.cacheControl.web
                    }
                  : require('../../assets/profilePhoto.png')
              }
              style={styles.userAvatar}
              resizeMode={FastImage.resizeMode.cover}
            />
          )}
        </View>
      </View>
    </>
  );
});

const ChatScreen = () => {
  const flatListRef = useRef(null);
  const keyboardListenersRef = useRef([]);

  // Only auto-scroll when a new message is added, not on every message change
  const prevMessagesLengthRef = useRef(0);
  // Track if this is the initial load to scroll to bottom
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Add keyboard listeners to handle keyboard appearance
  useEffect(() => {
    // When keyboard shows, scroll to the end of the messages
    const keyboardWillShowListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow',
      () => {
        if (flatListRef.current) {
          // Scroll immediately when keyboard appears
          flatListRef.current.scrollToEnd({animated: false});

          // Then scroll again after a short delay to ensure it's positioned correctly
          setTimeout(() => {
            flatListRef.current.scrollToEnd({animated: false});
          }, 50);
        }
      }
    );

    // When keyboard hides, adjust scroll position if needed
    const keyboardWillHideListener = Keyboard.addListener(
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide',
      () => {
        // Optional: adjust scroll position when keyboard hides if needed
      }
    );

    // Store listeners in ref for cleanup
    keyboardListenersRef.current = [keyboardWillShowListener, keyboardWillHideListener];

    // Cleanup function to remove listeners
    return () => {
      keyboardListenersRef.current.forEach(listener => listener.remove());
    };
  }, []);

  useEffect(() => {
    // Scroll to end if:
    // 1. This is the initial load and we have messages, OR
    // 2. A new message was added (length increased)
    const hasMessages = messages && messages.length > 0;
    const isNewMessage = messages && messages.length > prevMessagesLengthRef.current;

    if (hasMessages && (isInitialLoad || isNewMessage)) {
      if (flatListRef.current) {
        setTimeout(() => {
          flatListRef.current.scrollToEnd({animated: isInitialLoad ? false : true});
        }, isInitialLoad ? 100 : 500);
      }

      // Mark initial load as complete after first scroll
      if (isInitialLoad) {
        setIsInitialLoad(false);
      }
    }

    // Update the previous length reference
    prevMessagesLengthRef.current = messages ? messages.length : 0;
  }, [messages, isInitialLoad]);

  // Additional effect to handle scroll after loading is complete
  useEffect(() => {
    // When loading finishes and we have messages, ensure we scroll to the bottom
    if (!loading && messages && messages.length > 0 && isInitialLoad) {
      // Use multiple timeouts to ensure the FlatList has time to render
      setTimeout(() => {
        if (flatListRef.current) {
          flatListRef.current.scrollToEnd({animated: false});
        }
      }, 200);

      // Fallback scroll with longer delay
      setTimeout(() => {
        if (flatListRef.current) {
          flatListRef.current.scrollToEnd({animated: false});
        }
      }, 500);

      // Final fallback scroll
      setTimeout(() => {
        if (flatListRef.current) {
          flatListRef.current.scrollToEnd({animated: false});
        }
      }, 1000);
    }
  }, [loading, messages, isInitialLoad]);

  // Handle scroll when screen comes into focus
  useFocusEffect(
    useCallback(() => {
      // When the screen comes into focus, scroll to the bottom if we have messages
      if (messages && messages.length > 0) {
        // Use a timeout to ensure the screen is fully rendered
        setTimeout(() => {
          if (flatListRef.current) {
            flatListRef.current.scrollToEnd({animated: false});
          }
        }, 300);
      }
    }, [messages])
  );

  // Manual scroll to end function that can be called when needed
  const scrollToEnd = () => {
    if (flatListRef.current) {
      flatListRef.current.scrollToEnd({animated: true});
    }
  };
  const route = useRoute();
  const {messageId, isApiConversation} = route.params;
  const {userInfo, groupInfo, isGroupChat} = route.params || {};
  // Removed console.log statements for better performance

  // Handle different conversation types (local vs API)
  // const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingOlderMessages, setLoadingOlderMessages] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMoreMessages, setHasMoreMessages] = useState(true);
  // const [messages, setMessages] = useState([]);
  // Removed formattedDate state for performance optimization

  // Get conversationId from route params, with fallback to messageId if it's a string
  // This ensures backward compatibility with existing code
  let { conversationId = isApiConversation ? messageId : null } = route.params;

  // Get the current conversation data from Redux store
  const currentConversation = useSelector(state => state.currentConversation);

  // If we're in a group chat and don't have a conversationId, try to use the group ID from Redux
  useEffect(() => {
    if (isGroupChat && (!conversationId || conversationId === 'undefined') && currentConversation?.data?.group?._id) {
      conversationId = currentConversation.data.group._id;
    }
  }, [isGroupChat, conversationId, currentConversation]);
  const dispatch = useDispatch();
  const {
    messages,
    loading,
  } = useSelector((state) => ({
    messages: state.messages.messages,
    loading: state.messages.loading,
  }));

  // Get current user from Redux store - do this at the component level, not in renderItem
  const currentUser = useSelector(state => state.auth.user);
  const currentUserId = currentUser?.id || currentUser?._id;

  useEffect(() => {
    // Reset initial load state when conversation changes
    setIsInitialLoad(true);

    // Only fetch messages for API conversations with a valid conversationId
    if (isApiConversation) {
      if (!conversationId || typeof conversationId !== 'string') {
        return;
      }

      // Mark the conversation as read in AsyncStorage
      markMessageAsRead(conversationId).catch(() => {
        // Silent error handling for better performance
      });

      // Mark conversation as read in Redux store (reset unread count)
      dispatch(markConversationAsRead({ conversationId }));

      // Reset pagination state for initial load
      setCurrentPage(1);
      setHasMoreMessages(true);

      // Pass isGroupChat flag to use the correct endpoint
      dispatch(fetchConversationMessages({
        conversationId,
        isGroupChat: isGroupChat,
        page: 1,
        limit: 20
      }))
      .then((result) => {
        if (result.payload && result.payload.data) {
          const pagination = result.payload.data.pagination || {};
          setHasMoreMessages(pagination.page < pagination.totalPages);
        }
      });
    } else {
      // For local conversations, mark as read
      if (messageId) {
        markMessageAsRead(messageId).catch(() => {
          // Silent error handling for better performance
        });
      }
    }
  }, [conversationId, isApiConversation, messageId, dispatch]);

  // Removed date formatting useEffect for performance optimization

  // useEffect(() => {
  //   // Initialize messages based on conversation type
  //   if (isApiConversation) {
  //     // For API conversations, we would typically fetch messages from the API
  //     // For now, we'll just show a placeholder message
  //     setMessages([
  //       {
  //         id: 1,
  //         text: "Bu sohbet API'den geldi. Mesajlar henüz yüklenemedi.",
  //         timestamp: new Date(),
  //         isMe: false,
  //       }
  //     ]);
  //     setFormattedDate(formatDate(new Date()));
  //   } else {
  //     // For local messages (chatbot)
  //     const selectedMessage = messageList.find(item => item.id === messageId);
  //     if (selectedMessage && selectedMessage.messages) {
  //       setMessages(selectedMessage.messages);

  //       // Format the date of the last message
  //       const lastMessage = selectedMessage.messages[selectedMessage.messages.length - 1];
  //       if (lastMessage) {
  //         const lastMessageDate = new Date(lastMessage.timestamp);
  //         setFormattedDate(formatDate(lastMessageDate));
  //       }
  //     }
  //   }
  // }, [messageId, isApiConversation]);

  // Memoized helper function to format dates consistently
  const formatDate = useCallback((date) => {
    if (!date) return '';

    // Format: "02 May, ÖS 6:31"
    const day = date.getDate().toString().padStart(2, '0');

    // Get month name in Turkish
    const months = ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 'Tem', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'];
    const month = months[date.getMonth()];

    // Get time with AM/PM indicator
    const hours = date.getHours();
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const ampm = hours >= 12 ? 'ÖS' : 'ÖÖ';
    const displayHours = hours % 12 || 12; // Convert 0 to 12 for 12-hour format

    return `${day} ${month}, ${ampm} ${displayHours}:${minutes}`;
  }, []);

  const onScroll = useCallback((event) => {
    const { contentOffset } = event.nativeEvent;
    const contentOffsetY = contentOffset.y;

    // Check if user scrolled to the top to load older messages
    if (contentOffsetY <= 50 && !loadingOlderMessages && !loading && hasMoreMessages && isApiConversation && conversationId) {
      setLoadingOlderMessages(true);

      const nextPage = currentPage + 1;

      // Load older messages with pagination
      dispatch(fetchConversationMessages({
        conversationId,
        isGroupChat: isGroupChat,
        page: nextPage,
        limit: 20
      }))
      .then((result) => {
        if (result.payload && result.payload.data) {
          const pagination = result.payload.data.pagination || {};
          // Update pagination state
          setCurrentPage(nextPage);
          setHasMoreMessages(pagination.page < pagination.totalPages);
        }
        setLoadingOlderMessages(false);
      })
      .catch(() => {
        setLoadingOlderMessages(false);
      });
    }
  }, [loadingOlderMessages, loading, hasMoreMessages, isApiConversation, conversationId, currentPage, dispatch, isGroupChat]);

  const onRefresh = () => {
    setRefreshing(true);

    // Reset pagination when refreshing
    setCurrentPage(1);
    setHasMoreMessages(true);

    // For API conversations, refresh messages from the API
    if (isApiConversation && conversationId) {
      dispatch(fetchConversationMessages({
        conversationId,
        isGroupChat: isGroupChat,
        page: 1,
        limit: 20
      }))
      .then(() => {
        setRefreshing(false);
      })
      .catch(() => {
        setRefreshing(false);
      });
    } else {
      // Reset refreshing state after a delay for local conversations
      setTimeout(() => {
        setRefreshing(false);
      }, 1500);
    }
  };
  const [newMessage, setNewMessage] = useState('');

  const handleSendMessage = () => {
    if (newMessage.trim().length > 0) {
      // For API conversations, we would send the message to the API
      if (isApiConversation && conversationId) {
        console.log('Sending message to API:', newMessage);

        // Use the currentUser and currentUserId from the component level

        // Create a temporary message to show immediately in the UI
        const now = new Date();
        const tempMessageId = `temp-${Date.now()}`;
        const tempMessage = {
          _id: tempMessageId,
          text: newMessage,
          createdAt: now.toISOString(),
          timestamp: now.toISOString(), // Add timestamp for compatibility
          sender: {
            _id: currentUserId || 'currentUser', // Use actual user ID if available
            id: currentUserId, // Include both _id and id for compatibility
            username: currentUser?.username || 'You',
            profilePicture: currentUser?.profilePicture
          },
          isMe: true,
        };

        // Add the temporary message to the Redux store
        dispatch(addNewMessage(tempMessage));

        // Handle different message sending logic for group vs individual chats
        if (isGroupChat && groupInfo) {
          console.log('Sending message to group:', {
            groupId: groupInfo._id, // Use _id instead of groupId
            messageText: newMessage,
            conversationId: conversationId,
            participantsCount: groupInfo.participants?.length || 0,
            isGroupIdValid: !!groupInfo._id,
            groupIdType: typeof groupInfo._id
          });

          // Try to get the group ID from different sources
          const reduxGroupId = currentConversation?.data?.group?._id;
          const routeGroupId = groupInfo?._id;
          // In some cases, the conversationId might be the groupId
          const conversationAsGroupId = conversationId && typeof conversationId === 'string' &&
                                       /^[0-9a-fA-F]{24}$/.test(conversationId) ?
                                       conversationId : null;

          // Use the first available ID in this order: Redux, route params, conversationId
          const finalGroupId = reduxGroupId || routeGroupId || conversationAsGroupId;

          console.log('Group ID sources:', {
            reduxGroupId,
            routeGroupId,
            conversationAsGroupId,
            finalGroupId,
            usingReduxId: !!reduxGroupId,
            usingRouteId: !reduxGroupId && !!routeGroupId,
            usingConversationId: !reduxGroupId && !routeGroupId && !!conversationAsGroupId
          });

          // Check if we have a valid group ID
          if (!finalGroupId) {
            console.error('Error: No valid group ID found for sending message');
            // You might want to show an error message to the user here
            return;
          }

          // For group chats, we need to specify the groupId
          dispatch(sendMessage({
            conversationId,
            messageData: {
              text: newMessage,
              groupId: finalGroupId,
              tempMessageId: tempMessageId
            }
          }))
          .then(result => {
            if (result && result.payload) {
              console.log('✅ GROUP MESSAGE SENT SUCCESSFULLY ✅');
              console.log('Group message details:', {
                success: !!result.payload,
                messageId: result.payload?.message?._id,
                text: result.payload?.message?.text,
                timestamp: new Date().toISOString(),
                responseStatus: result.payload?.status,
                fullMessage: result.payload?.message
              });
            } else {
              console.warn('⚠️ Group message sent but response format unexpected:', result);
            }
          })
          .catch(error => {
            console.error('❌ ERROR SENDING GROUP MESSAGE ❌');
            console.error('Error details:', {
              message: error.message,
              status: error.response?.status,
              statusText: error.response?.statusText,
              data: error.response?.data,
              config: error.config ? {
                url: error.config.url,
                method: error.config.method,
                baseURL: error.config.baseURL
              } : 'No config available'
            });
          });
        } else {
          // Extract the recipient user ID from userInfo for individual chats
          const recipientId = userInfo?.userId || route.params?.recipientId;

          if (!recipientId) {
            console.error('Error: No recipient ID found for sending message');
            // You might want to show an error message to the user here
          } else {
            console.log('Sending message with recipientId:', {
              recipientId: recipientId,
              messageText: newMessage,
              conversationId: conversationId
            });

            // Send the message to the API with the correct recipient ID
            dispatch(sendMessage({
              conversationId,
              messageData: {
                text: newMessage,
                recipientId: recipientId,
                tempMessageId: tempMessageId
              }
            }))
            .then(result => {
              console.log('Individual message sent successfully:', {
                success: !!result.payload,
                messageId: result.payload?.message?._id,
                timestamp: new Date().toISOString()
              });
            })
            .catch(error => {
              console.error('Error sending individual message:', error);
            });
          }
        }

        // Clear the input
        setNewMessage('');

        // Scroll to the end after sending a message
        setTimeout(() => {
          scrollToEnd();
          // Ensure keyboard doesn't cover the latest message
          if (Platform.OS === 'ios') {
            setTimeout(() => {
              scrollToEnd();
            }, 300);
          }
        }, 300);
      }
      // For local conversations (not connected to API)
      else {
        console.log('Local message, not sending to API');
        setNewMessage('');
      }
    }
  };
// Optimized for performance - removed console.log
  return (
    <KeyboardAvoidingView
      style={styles.keyboardAvoidingView}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}>
      <SafeAreaView style={styles.container}>
        <Header
          userInfo={userInfo}
          groupInfo={groupInfo}
          isGroupChat={isGroupChat}
        />

        <View style={styles.chatContainer}>
          <FlatList
            data={messages}
            keyExtractor={(item, index) => item._id ? item._id.toString() : index.toString()}
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="on-drag"
            onScroll={onScroll}
            scrollEnabled={true}
            showsVerticalScrollIndicator={false}
            style={styles.flatList}
            contentContainerStyle={styles.flatListContent}
            ref={flatListRef}
            removeClippedSubviews={true}
            windowSize={5}
            maxToRenderPerBatch={5}
            initialNumToRender={10}
            updateCellsBatchingPeriod={100}
            scrollEventThrottle={16}
            getItemLayout={null}
            onContentSizeChange={() => {
              // Scroll to bottom when content size changes (messages loaded)
              if (messages && messages.length > 0 && isInitialLoad) {
                setTimeout(() => {
                  if (flatListRef.current) {
                    flatListRef.current.scrollToEnd({animated: false});
                  }
                }, 100);
              }
            }}
            maintainVisibleContentPosition={
              loadingOlderMessages ? {
                minIndexForVisible: 0,
                autoscrollToTopThreshold: 100,
              } : undefined
            }
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={onRefresh}
                colors={["transparent"]}
                tintColor="transparent"
              />
            }
            ListHeaderComponent={
              <>
                {loadingOlderMessages && hasMoreMessages && (
                  <View style={styles.olderMessagesLoaderContainer}>
                    <Loader isVisible={true} speed="normal" />
                  </View>
                )}
                <UserInfo
                  userInfo={userInfo}
                  groupInfo={groupInfo}
                  isGroupChat={isGroupChat}
                  key={refreshing ? 'refreshed' : 'normal'}
                />
              </>
            }
              renderItem={useCallback(({item, index}) => (
                <MessageItem
                  item={item}
                  index={index}
                  currentUserId={currentUserId}
                  currentUser={currentUser}
                  userInfo={userInfo}
                  messages={messages}
                  formatDate={formatDate}
                />
              ), [currentUserId, currentUser, userInfo, messages, formatDate])}
            />

          {/* Input Alanı */}
          <View style={styles.inputContainer}>
            <TouchableOpacity style={styles.inputBtn} activeOpacity={0.5}>
              <SvgAddImage />
            </TouchableOpacity>
            <TextInput
              value={newMessage}
              onChangeText={setNewMessage}
              style={styles.placeholder}
              placeholder="Mesaj yaz..."
              selectionColor="#D134AA"
              onFocus={() => {
                // Scroll to end when input is focused (keyboard appears)
                setTimeout(() => {
                  if (flatListRef.current) {
                    flatListRef.current.scrollToEnd({animated: true});
                  }
                }, 200);
              }}
            />
            <TouchableOpacity
              onPress={handleSendMessage}
              style={styles.inputBtn}
              activeOpacity={0.5}>
              <SvgSendBtn />
            </TouchableOpacity>
          </View>
        </View>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

export default ChatScreen;

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    flex: 1,
  },
  messageLoaderContainer: {
    paddingVertical: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  olderMessagesLoaderContainer: {
    paddingVertical: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#fff',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  chatContainer: {
    flex: 1,
  },
  flatList: {
    flex: 1,
  },
  flatListContent: {
    paddingHorizontal: 10,
    paddingBottom: 100, // Increased padding for better scrolling
  },
  scrollToBottomButton: {
    position: 'absolute',
    right: 15,
    bottom: 15,
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
  },
  scrollToBottomButtonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  lastMessageDate: {
    fontSize: 12,
    fontWeight: '400',
    color: '#9D9C9C',
    alignSelf: 'center',
    textAlign: 'center',
    marginTop: 20,
    marginBottom: 15,
  },
  messageContainer: {
    marginHorizontal: 7,
    marginVertical: 3,
  },
  myMessageContainer: {
    alignSelf: 'flex-end',
    marginLeft: 50, // Add space on the left for sent messages
  },
  otherMessageContainer: {
    alignSelf: 'flex-start',
    marginRight: 50, // Add space on the right for received messages
  },
  userAvatar: {
    width: 35,
    height: 35,
    borderRadius: 17.5,
    marginHorizontal: 7,
    alignSelf: 'center',
  },
  emptyAvatar: {
    width: 35,
    height: 35,
    marginHorizontal: 7,
  },
  messageBubble: {
    maxWidth: '75%',
    minWidth: 60, // Add minimum width to ensure short messages display properly
    padding: 10,
    paddingHorizontal: 15, // Increase horizontal padding for better text visibility
    borderRadius: 10,
    justifyContent: 'center', // Center content vertically
    alignItems: 'flex-start', // Align content to the start horizontally
    overflow: 'visible', // Ensure text doesn't get cut off
  },
  myMessage: {
    alignSelf: 'flex-end',
    backgroundColor: '#D134AA',
    borderRadius: 25,
  },
  otherMessage: {
    alignSelf: 'flex-start',
    backgroundColor: '#F1F1F1',
    borderRadius: 25,
  },
  messageText: {
    color: '#181818',
    fontSize: 12,
    fontWeight: '500',
    paddingHorizontal: 5, // Use padding instead of margin for better text containment
    flexShrink: 1, // Allow text to shrink if needed
    flexWrap: 'wrap', // Ensure text wraps properly
    textAlign: 'left', // Ensure text is left-aligned
    alignSelf: 'flex-start', // Align text to the start of the container
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderRadius: 59,
    paddingHorizontal: 10,
    paddingVertical: 5,
    marginTop: 10,
    marginBottom: Platform.OS === 'ios' ? 10 : 10,
    marginHorizontal: 20,
    height: height * (Platform.OS === 'android' ? 0.06 : 0.05),
    borderColor: '#000000',
    backgroundColor: '#FFFFFF',
  },
  placeholder: {
    flex: 1,
    fontWeight: '500',
    paddingVertical: 8,
    color: '#000000',
  },
  inputBtn: {
    padding: 8,
  },
  errorText: {
    textAlign: 'center',
    marginTop: 20,
    fontSize: 16,
    color: 'red',
  },
  messageDateText: {
    fontSize: 12,
    fontWeight: '400',
    color: '#9D9C9C',
    alignSelf: 'center',
    textAlign: 'center',
    marginTop: 15,
    marginBottom: 10,
  },
});

