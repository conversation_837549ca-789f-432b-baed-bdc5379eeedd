import { createContext, useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { messageList as initialMessages } from '../../utils/helpers';

export const MessageContext = createContext();

export const MessageProvider = ({ children }) => {
  const [messages, setMessages] = useState(initialMessages);
  const [hasUnreadMessages, setHasUnreadMessages] = useState(false);

  // Get conversations from Redux store
  const { conversations } = useSelector((state) => state.conversations);

  useEffect(() => {
    // Check for unread messages using Redux conversations data
    if (conversations && conversations.length > 0) {
      // Check if any conversation has unread messages
      const unreadExists = conversations.some(conversation => {
        // Check unreadCount from API data
        if (conversation.unreadCount && conversation.unreadCount > 0) {
          return true;
        }

        // Fallback: check individual messages for isRead property (for local data compatibility)
        if (conversation.messages && Array.isArray(conversation.messages)) {
          return conversation.messages.some(message => !message.isRead);
        }

        return false;
      });

      setHasUnreadMessages(unreadExists);
      console.log('MessageContext: Unread messages check result:', unreadExists);
    } else {
      // Fallback to local messages if no Redux conversations
      const unreadExists = messages.some(chat =>
        chat.messages && chat.messages.some(message => !message.isRead)
      );
      setHasUnreadMessages(unreadExists);
      console.log('MessageContext: Using fallback local messages, unread result:', unreadExists);
    }
  }, [conversations, messages]);

  // Mesajı okundu olarak işaretleme fonksiyonu
  const markAsRead = (messageId) => {
    setMessages(prevMessages =>
      prevMessages.map(chat => {
        if (chat.id === messageId) {
          return {
            ...chat,
            messages: chat.messages.map(msg => ({ ...msg, isRead: true }))
          };
        }
        return chat;
      })
    );
  };

  return (
    <MessageContext.Provider value={{ messages, hasUnreadMessages, markAsRead }}>
      {children}
    </MessageContext.Provider>
  );
};
