import { createSlice } from "@reduxjs/toolkit";
import {
  fetchConversations,
  startNewConversation,
  startNewGroupConversation,
  deleteConversation
} from "../actions/conversationsActions";

const conversationsSlice = createSlice({
  name: 'conversations',
  initialState: {
    conversations: [],
    currentConversation: null,
    loading: false,
    error: null,
    total: 0,
    totalPages: 1,
    currentPage: 1,
  },
  reducers: {
    // Yeni mesaj geldiğinde conversations'ı güncellemek için
    updateConversationLastMessage: (state, action) => {
      const { conversationId, message } = action.payload;
      console.log('Updating conversation last message:', { conversationId, messageText: message.text });

      // Find conversation by _id (API conversations) or id (local conversations)
      const index = state.conversations.findIndex(c => c._id === conversationId || c.id === conversationId);

      if (index !== -1) {
        console.log('Found conversation at index:', index);

        // Update the last message
        state.conversations[index].lastMessage = {
          _id: message._id,
          id: message._id, // Include both for compatibility
          text: message.text,
          createdAt: message.createdAt,
          date: message.createdAt // Include both for compatibility
        };
        state.conversations[index].updatedAt = message.createdAt;

        // Reset unread count since this is a message sent by the current user
        state.conversations[index].unreadCount = 0;

        // En son güncelleneni en üste taşı
        const updatedConv = state.conversations.splice(index, 1)[0];
        state.conversations.unshift(updatedConv);

        console.log('Conversation updated and moved to top');
      } else {
        console.log('Conversation not found for ID:', conversationId);
      }
    },
    clearConversations: (state) => {
      state.conversations = [];
      state.total = 0;
      state.totalPages = 1;
      state.currentPage = 1;
    },
    resetConversationState: (state) => {
      console.log('Conversation state resetlendi');
      state.currentConversation = null;
      state.loading = false;
      state.error = null;
      state.success = false;
    },
    // Mark conversation as read (reset unread count)
    markConversationAsRead: (state, action) => {
      const { conversationId } = action.payload;
      console.log('Marking conversation as read:', conversationId);

      // Find conversation by _id (API conversations) or id (local conversations)
      const index = state.conversations.findIndex(c => c._id === conversationId || c.id === conversationId);

      if (index !== -1) {
        // Reset unread count to 0
        state.conversations[index].unreadCount = 0;
        console.log('Conversation marked as read, unreadCount reset to 0');
      } else {
        console.log('Conversation not found for marking as read:', conversationId);
      }
    },
    // Handle incoming messages (increment unread count)
    updateConversationWithIncomingMessage: (state, action) => {
      const { conversationId, message, currentUserId } = action.payload;
      console.log('Updating conversation with incoming message:', { conversationId, messageText: message.text, senderId: message.sender?._id });

      // Find conversation by _id (API conversations) or id (local conversations)
      const index = state.conversations.findIndex(c => c._id === conversationId || c.id === conversationId);

      if (index !== -1) {
        console.log('Found conversation at index:', index);

        // Update the last message
        state.conversations[index].lastMessage = {
          _id: message._id,
          id: message._id, // Include both for compatibility
          text: message.text,
          createdAt: message.createdAt,
          date: message.createdAt // Include both for compatibility
        };
        state.conversations[index].updatedAt = message.createdAt;

        // Only increment unread count if the message is from someone else
        const senderId = message.sender?._id || message.sender?.id;
        if (senderId && senderId !== currentUserId) {
          state.conversations[index].unreadCount = (state.conversations[index].unreadCount || 0) + 1;
          console.log('Incremented unread count to:', state.conversations[index].unreadCount);
        } else {
          console.log('Message from current user, not incrementing unread count');
        }

        // Move conversation to top
        const updatedConv = state.conversations.splice(index, 1)[0];
        state.conversations.unshift(updatedConv);

        console.log('Conversation updated and moved to top');
      } else {
        console.log('Conversation not found for ID:', conversationId);
      }
    },
    resetAllConversations: (state) => {
      console.log('[CONVERSATIONS SLICE] Resetting all conversations to initial state');
      Object.assign(state, {
        conversations: [],
        currentConversation: null,
        loading: false,
        error: null,
        total: 0,
        totalPages: 1,
        currentPage: 1,
      });
    }
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchConversations.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchConversations.fulfilled, (state, action) => {
        state.loading = false;
        state.conversations = action.payload.data.conversations;
        state.total = action.payload.total;
        state.totalPages = action.payload.totalPages;
        state.currentPage = action.payload.currentPage;
      })
      .addCase(fetchConversations.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload?.message || action.error.message;
      })
      .addCase(startNewConversation.pending, (state) => {
        console.log('Yeni sohbet başlatma isteği gönderiliyor...');
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(startNewConversation.fulfilled, (state, action) => {
        console.log('Yeni sohbet başarıyla oluşturuldu:', action.payload);
        state.loading = false;
        state.success = true;
        state.currentConversation = action.payload;

        // We don't add the conversation to the list until a message is sent
        // This ensures that conversations without messages don't appear in the list
      })
      .addCase(startNewConversation.rejected, (state, action) => {
        console.log('Yeni sohbet oluşturma hatası:', action.payload);
        state.loading = false;
        state.error = action.payload;
      })
      .addCase(startNewGroupConversation.pending, (state) => {
        console.log('Yeni grup sohbeti başlatma isteği gönderiliyor...');
        state.loading = true;
        state.error = null;
        state.success = false;
      })
      .addCase(startNewGroupConversation.fulfilled, (state, action) => {
        console.log('Yeni grup sohbeti başarıyla oluşturuldu:', action.payload);
        state.loading = false;
        state.success = true;
        state.currentConversation = action.payload;
      })
      .addCase(startNewGroupConversation.rejected, (state, action) => {
        console.log('Yeni grup sohbeti oluşturma hatası:', action.payload);
        state.loading = false;
        state.error = action.payload;
      })
      // Delete conversation cases
      .addCase(deleteConversation.pending, (state) => {
        console.log('Sohbet silme isteği gönderiliyor...');
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteConversation.fulfilled, (state, action) => {
        console.log('Sohbet başarıyla silindi:', action.payload);
        state.loading = false;

        // Remove the deleted conversation from the state
        const { conversationId } = action.payload;
        state.conversations = state.conversations.filter(
          conversation => conversation._id !== conversationId
        );

        // If the deleted conversation was the current conversation, reset it
        if (state.currentConversation && state.currentConversation._id === conversationId) {
          state.currentConversation = null;
        }
      })
      .addCase(deleteConversation.rejected, (state, action) => {
        console.log('Sohbet silme hatası:', action.payload);
        state.loading = false;
        state.error = action.payload?.message || action.error.message;
      });
  },
});

export const { updateConversationLastMessage, clearConversations, resetConversationState, markConversationAsRead, updateConversationWithIncomingMessage, resetAllConversations } = conversationsSlice.actions;
export default conversationsSlice.reducer;